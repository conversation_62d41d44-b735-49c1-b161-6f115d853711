import java.awt.*;
import java.awt.event.*;
import javax.swing.*;

public class FocusEnforcer {
    private static final String INTELLIJ_WINDOW_TITLE = "IntelliJ IDEA";
    private static Robot robot;
    private static TrayIcon trayIcon;

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                robot = new Robot();
                setupSystemTray();
                startWindowMonitor();
            } catch (AWTException e) {
                e.printStackTrace();
                System.exit(1);
            }
        });
    }

    private static void setupSystemTray() {
        if (!SystemTray.isSupported()) {
            System.out.println("SystemTray is not supported");
            return;
        }

        SystemTray tray = SystemTray.getSystemTray();
        Image image = Toolkit.getDefaultToolkit().createImage("icon.png"); // You'll need to provide an icon
        trayIcon = new TrayIcon(image, "Focus Enforcer");
        trayIcon.setImageAutoSize(true);

        try {
            tray.add(trayIcon);
        } catch (AWTException e) {
            e.printStackTrace();
        }
    }

    private static void startWindowMonitor() {
        Thread monitorThread = new Thread(() -> {
            while (true) {
                Window[] windows = Window.getWindows();
                for (Window window : windows) {
                    if (window.isVisible() && window instanceof Frame) {
                        Frame frame = (Frame) window;
                        String title = frame.getTitle();
                        
                        if (!title.contains(INTELLIJ_WINDOW_TITLE)) {
                            frame.dispose();
                            showNotification("Do your work!");
                        }
                    }
                }

                try {
                    Thread.sleep(1000); // Check every second
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        });

        monitorThread.setDaemon(true);
        monitorThread.start();
    }

    private static void showNotification(String message) {
        if (trayIcon != null) {
            trayIcon.displayMessage("Focus Enforcer", 
                                  message, 
                                  TrayIcon.MessageType.WARNING);
        }
    }
}