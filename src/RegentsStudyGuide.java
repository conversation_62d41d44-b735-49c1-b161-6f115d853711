import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.HashMap;
import java.util.Map;

public class RegentsStudyGuide {
    private JFrame frame;
    private CardLayout cardLayout;
    private JPanel mainPanel;
    private Map<String, StudySubject> subjects;

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                new RegentsStudyGuide().initialize();
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    private void initialize() {
        // Initialize main frame
        frame = new JFrame("NYS Regents Study Guide");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(1000, 700);
        frame.setLocationRelativeTo(null);

        // Create layout and main panel
        cardLayout = new CardLayout();
        mainPanel = new JPanel(cardLayout);

        // Initialize study subjects with detailed content
        initializeSubjects();

        // Create navigation panel with improved styling
        JPanel navPanel = new JPanel(new GridLayout(1, 4));
        navPanel.setBackground(new Color(70, 130, 180));
        navPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        for (String subjectName : subjects.keySet()) {
            JButton subjectButton = createStyledButton(subjectName);
            subjectButton.addActionListener(new SubjectButtonListener(subjectName));
            navPanel.add(subjectButton);
        }

        // Add components to frame
        frame.add(navPanel, BorderLayout.NORTH);
        frame.add(mainPanel, BorderLayout.CENTER);

        frame.setVisible(true);
    }

    private JButton createStyledButton(String text) {
        JButton button = new JButton(text);
        button.setBackground(new Color(100, 149, 237));
        button.setForeground(Color.WHITE);
        button.setFont(new Font("Arial", Font.BOLD, 14));
        button.setFocusPainted(false);
        button.setBorder(BorderFactory.createEmptyBorder(10, 15, 10, 15));
        return button;
    }

    private void initializeSubjects() {
        subjects = new HashMap<>();

        // Living Environment with detailed content
        StudySubject livingEnv = new StudySubject("Living Environment");
        livingEnv.addUnit("Unit 1: Scientific Inquiry",
                "Key Topics:\n" +
                        "- Scientific Method: Observation, hypothesis, experimentation, conclusion\n" +
                        "- Experimental Design: Control groups, variables (independent/dependent)\n" +
                        "- Data Analysis: Graphs, tables, statistical analysis\n" +
                        "- Lab Safety: Proper equipment use, handling of biological materials\n\n" +
                        "Important Concepts:\n" +
                        "• Peer review ensures validity of scientific findings\n" +
                        "• Theories are well-supported explanations (not just guesses)\n" +
                        "• Metric system is standard in scientific measurements");

        livingEnv.addUnit("Unit 2: Biochemistry",
                "Key Topics:\n" +
                        "- Organic Compounds: Carbohydrates, lipids, proteins, nucleic acids\n" +
                        "- Enzymes: Catalysts that speed up reactions, affected by pH/temperature\n" +
                        "- Homeostasis: Maintaining stable internal conditions\n" +
                        "- pH Scale: Acids (0-6), bases (8-14), neutral (7)\n\n" +
                        "Important Concepts:\n" +
                        "• Water is polar and excellent solvent (universal solvent)\n" +
                        "• Lock-and-key model explains enzyme specificity\n" +
                        "• Feedback mechanisms help maintain homeostasis");

        // Add remaining Living Environment units...
        livingEnv.addUnit("Unit 3: Cells", getCellContent());
        livingEnv.addUnit("Unit 4: Human Body Systems", getHumanBodyContent());
        livingEnv.addUnit("Unit 5: Reproduction & Genetics", getGeneticsContent());
        subjects.put("Living Environment", livingEnv);

        // Global History with detailed content
        StudySubject globalHistory = new StudySubject("Global History");
        globalHistory.addUnit("Unit 1: Ancient World", getAncientWorldContent());
        globalHistory.addUnit("Unit 2: Middle Ages", getMiddleAgesContent());
        globalHistory.addUnit("Unit 3: Early Modern", getEarlyModernContent());
        globalHistory.addUnit("Unit 4: Modern Era", getModernEraContent());
        globalHistory.addUnit("Unit 5: 20th Century", get20thCenturyContent());
        subjects.put("Global History", globalHistory);

        // Geometry with detailed content
        StudySubject geometry = new StudySubject("Geometry");
        geometry.addUnit("Unit 1: Basics", getGeometryBasicsContent());
        geometry.addUnit("Unit 2: Triangles", getTrianglesContent());
        geometry.addUnit("Unit 3: Polygons", getPolygonsContent());
        geometry.addUnit("Unit 4: Circles", getCirclesContent());
        geometry.addUnit("Unit 5: 3D Geometry", get3DGeometryContent());
        subjects.put("Geometry", geometry);

        // Java Programming with detailed content
        StudySubject java = new StudySubject("Java Programming");
        java.addUnit("Unit 1: Basics", getJavaBasicsContent());
        java.addUnit("Unit 2: Control Structures", getControlStructuresContent());
        java.addUnit("Unit 3: OOP", getOOPContent());
        java.addUnit("Unit 4: Data Structures", getDataStructuresContent());
        java.addUnit("Unit 5: GUI Programming", getGUIContent());
        subjects.put("Java Programming", java);

        // Finalize and add all subject panels to main panel
        for (StudySubject subject : subjects.values()) {
            subject.finalizePanel();
            mainPanel.add(subject.getPanel(), subject.getName());
        }
    }

    // Content methods for each unit (sample implementations)
    private String getCellContent() {
        return "Key Topics:\n- Cell Theory\n- Organelles and their functions\n- Cell membrane structure\n- Active vs passive transport";
    }

    private String getHumanBodyContent() {
        return "Key Topics:\n- Nervous system\n- Circulatory system\n- Respiratory system\n- Digestive system\n- Homeostasis mechanisms";
    }

    private String getGeneticsContent() {
        return "Key Topics:\n- DNA structure\n- Mitosis vs meiosis\n- Mendelian genetics\n- Genetic engineering\n- Mutations";
    }

    private String getAncientWorldContent() {
        return "Key Topics:\n- River valley civilizations\n- Classical empires\n- Major religions\n- Trade routes\n- Cultural achievements";
    }

    private String getMiddleAgesContent() {
        return "Key Topics:\n- Feudal systems\n- Byzantine Empire\n- Islamic Golden Age\n- Crusades\n- Medieval culture and technology";
    }

    private String getEarlyModernContent() {
        return "Key Topics:\n- Renaissance\n- Reformation\n- Age of Exploration\n- Scientific Revolution\n- Enlightenment";
    }

    private String getModernEraContent() {
        return "Key Topics:\n- Industrial Revolution\n- Nationalism\n- Imperialism\n- World War I\n- Russian Revolution";
    }

    private String get20thCenturyContent() {
        return "Key Topics:\n- Great Depression\n- World War II\n- Cold War\n- Decolonization\n- Globalization";
    }

    private String getGeometryBasicsContent() {
        return "Key Topics:\n- Points, lines, and planes\n- Angles and angle relationships\n- Coordinate geometry\n- Transformations\n- Constructions";
    }

    private String getTrianglesContent() {
        return "Key Topics:\n- Triangle congruence\n- Triangle similarity\n- Special right triangles\n- Pythagorean theorem\n- Triangle centers";
    }

    private String getPolygonsContent() {
        return "Key Topics:\n- Quadrilaterals\n- Regular polygons\n- Interior and exterior angles\n- Area formulas\n- Coordinate geometry of polygons";
    }

    private String getCirclesContent() {
        return "Key Topics:\n- Circle equations\n- Arc measures\n- Inscribed angles\n- Tangent lines\n- Sector and segment areas";
    }

    private String get3DGeometryContent() {
        return "Key Topics:\n- Polyhedra\n- Surface area\n- Volume\n- Cross-sections\n- Coordinate geometry in 3D";
    }

    private String getJavaBasicsContent() {
        return "Key Topics:\n- Data types and variables\n- Operators\n- Input/output\n- Methods\n- Arrays";
    }

    private String getControlStructuresContent() {
        return "Key Topics:\n- Conditional statements\n- Loops\n- Switch statements\n- Exception handling\n- Method recursion";
    }

    private String getOOPContent() {
        return "Key Topics:\n- Classes and objects\n- Inheritance\n- Polymorphism\n- Encapsulation\n- Interfaces and abstract classes";
    }

    private String getDataStructuresContent() {
        return "Key Topics:\n- Lists\n- Sets\n- Maps\n- Stacks and queues\n- Sorting and searching algorithms";
    }

    private String getGUIContent() {
        return "Key Topics:\n- Swing components\n- Layouts\n- Event handling\n- Graphics\n- Animation";
    }

    private class SubjectButtonListener implements ActionListener {
        private String subjectName;

        public SubjectButtonListener(String subjectName) {
            this.subjectName = subjectName;
        }

        @Override
        public void actionPerformed(ActionEvent e) {
            cardLayout.show(mainPanel, subjectName);
        }
    }

    private class StudySubject {
        private String name;
        private JPanel panel;
        private CardLayout subjectCardLayout;
        private Map<String, String> units;
        private JList<String> unitList;

        public StudySubject(String name) {
            this.name = name;
            this.units = new HashMap<>();
            this.subjectCardLayout = new CardLayout();
            this.panel = new JPanel(new BorderLayout());
            // Don't initialize panel yet - wait until units are added
        }

        private void initializeSubjectPanel() {
            // Create sidebar with unit list
            DefaultListModel<String> listModel = new DefaultListModel<>();
            for (String unitName : units.keySet()) {
                listModel.addElement(unitName);
            }

            unitList = new JList<>(listModel);
            unitList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
            unitList.setFont(new Font("Arial", Font.PLAIN, 14));
            unitList.setFixedCellHeight(40);
            unitList.setBackground(new Color(240, 240, 240));
            unitList.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

            unitList.addListSelectionListener(e -> {
                if (!e.getValueIsAdjusting()) {
                    String selectedUnit = unitList.getSelectedValue();
                    if (selectedUnit != null) {
                        subjectCardLayout.show((JPanel)panel.getComponent(1), selectedUnit);
                    }
                }
            });

            JScrollPane listScrollPane = new JScrollPane(unitList);
            listScrollPane.setPreferredSize(new Dimension(200, 600));

            // Create content panel with CardLayout
            JPanel contentPanel = new JPanel(subjectCardLayout);

            // Create a panel for each unit
            for (Map.Entry<String, String> entry : units.entrySet()) {
                JTextArea contentArea = new JTextArea(entry.getValue());
                contentArea.setEditable(false);
                contentArea.setLineWrap(true);
                contentArea.setWrapStyleWord(true);
                contentArea.setFont(new Font("Arial", Font.PLAIN, 16));
                contentArea.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

                JScrollPane scrollPane = new JScrollPane(contentArea);
                contentPanel.add(scrollPane, entry.getKey());
            }

            // Add welcome panel
            JTextArea welcomeArea = new JTextArea("Welcome to the " + name + " Study Guide!\n\n" +
                    "Select a unit from the left sidebar to begin studying.\n\n" +
                    "This guide covers all key concepts from the NYS Regents exam.");
            welcomeArea.setEditable(false);
            welcomeArea.setFont(new Font("Arial", Font.PLAIN, 18));
            welcomeArea.setBorder(BorderFactory.createEmptyBorder(50, 50, 50, 50));
            contentPanel.add(new JScrollPane(welcomeArea), "Welcome");

            // Add components to subject panel
            panel.add(listScrollPane, BorderLayout.WEST);
            panel.add(contentPanel, BorderLayout.CENTER);

            subjectCardLayout.show(contentPanel, "Welcome");
        }

        public void addUnit(String unitName, String content) {
            units.put(unitName, content);
        }

        // Initialize the panel after all units have been added
        public void finalizePanel() {
            initializeSubjectPanel();
        }

        public JPanel getPanel() {
            return panel;
        }

        public String getName() {
            return name;
        }
    }
}