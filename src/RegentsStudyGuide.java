import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.HashMap;
import java.util.Map;

public class RegentsStudyGuide {
    private JFrame frame;
    private CardLayout cardLayout;
    private JPanel mainPanel;
    private Map<String, StudySubject> subjects;

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                new RegentsStudyGuide().initialize();
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    private void initialize() {
        // Initialize main frame
        frame = new JFrame("NYS Regents Study Guide");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(800, 600);
        frame.setLocationRelativeTo(null);

        // Create layout and main panel
        cardLayout = new CardLayout();
        mainPanel = new JPanel(cardLayout);

        // Initialize study subjects
        initializeSubjects();

        // Create navigation panel
        JPanel navPanel = new JPanel(new GridLayout(1, 4));

        for (String subjectName : subjects.keySet()) {
            JButton subjectButton = new JButton(subjectName);
            subjectButton.addActionListener(new SubjectButtonListener(subjectName));
            navPanel.add(subjectButton);
        }

        // Add components to frame
        frame.add(navPanel, BorderLayout.NORTH);
        frame.add(mainPanel, BorderLayout.CENTER);

        frame.setVisible(true);
    }

    private void initializeSubjects() {
        subjects = new HashMap<>();

        // Living Environment
        StudySubject livingEnv = new StudySubject("Living Environment");
        livingEnv.addUnit("Unit 1: Scientific Inquiry", "Key concepts: Scientific method, experimental design, data analysis");
        livingEnv.addUnit("Unit 2: Biochemistry", "Key concepts: Organic compounds, enzymes, pH, homeostasis");
        livingEnv.addUnit("Unit 3: Cells", "Key concepts: Cell structure, organelles, diffusion, osmosis");
        livingEnv.addUnit("Unit 4: Human Body Systems", "Key concepts: Nervous, endocrine, circulatory systems");
        livingEnv.addUnit("Unit 5: Reproduction & Genetics", "Key concepts: Mitosis, meiosis, DNA, genetic engineering");
        subjects.put("Living Environment", livingEnv);

        // Global History
        StudySubject globalHistory = new StudySubject("Global History");
        globalHistory.addUnit("Unit 1: Ancient World", "Key concepts: River valley civilizations, classical empires");
        globalHistory.addUnit("Unit 2: Middle Ages", "Key concepts: Feudalism, world religions, trade networks");
        globalHistory.addUnit("Unit 3: Early Modern", "Key concepts: Renaissance, Reformation, exploration");
        globalHistory.addUnit("Unit 4: Modern Era", "Key concepts: Industrial Revolution, nationalism, imperialism");
        globalHistory.addUnit("Unit 5: 20th Century", "Key concepts: World Wars, Cold War, globalization");
        subjects.put("Global History", globalHistory);

        // Geometry
        StudySubject geometry = new StudySubject("Geometry");
        geometry.addUnit("Unit 1: Basics", "Key concepts: Points, lines, planes, angles, proofs");
        geometry.addUnit("Unit 2: Triangles", "Key concepts: Congruence, similarity, Pythagorean theorem");
        geometry.addUnit("Unit 3: Polygons", "Key concepts: Quadrilaterals, area, perimeter");
        geometry.addUnit("Unit 4: Circles", "Key concepts: Arcs, chords, tangents, area, circumference");
        geometry.addUnit("Unit 5: 3D Geometry", "Key concepts: Volume, surface area, cross-sections");
        subjects.put("Geometry", geometry);

        // Java Programming
        StudySubject java = new StudySubject("Java Programming");
        java.addUnit("Unit 1: Basics", "Key concepts: Variables, data types, operators, I/O");
        java.addUnit("Unit 2: Control Structures", "Key concepts: Conditionals, loops, methods");
        java.addUnit("Unit 3: OOP", "Key concepts: Classes, objects, inheritance, polymorphism");
        java.addUnit("Unit 4: Data Structures", "Key concepts: Arrays, ArrayLists, searching, sorting");
        java.addUnit("Unit 5: GUI Programming", "Key concepts: Swing components, event handling");
        subjects.put("Java Programming", java);

        // Add all subject panels to main panel
        for (StudySubject subject : subjects.values()) {
            mainPanel.add(subject.getPanel(), subject.getName());
        }
    }

    private class SubjectButtonListener implements ActionListener {
        private String subjectName;

        public SubjectButtonListener(String subjectName) {
            this.subjectName = subjectName;
        }

        @Override
        public void actionPerformed(ActionEvent e) {
            cardLayout.show(mainPanel, subjectName);
        }
    }

    private class StudySubject {
        private String name;
        private JPanel panel;
        private CardLayout subjectCardLayout;
        private Map<String, String> units;

        public StudySubject(String name) {
            this.name = name;
            this.units = new HashMap<>();
            this.subjectCardLayout = new CardLayout();
            this.panel = new JPanel(subjectCardLayout);
            initializeSubjectPanel();
        }

        private void initializeSubjectPanel() {
            // Create navigation panel for units
            JPanel unitNavPanel = new JPanel(new GridLayout(1, 5));

            // Create a panel for each unit
            for (String unitName : units.keySet()) {
                JButton unitButton = new JButton(unitName);
                unitButton.addActionListener(new UnitButtonListener(unitName));
                unitNavPanel.add(unitButton);

                JTextArea contentArea = new JTextArea(units.get(unitName));
                contentArea.setEditable(false);
                contentArea.setLineWrap(true);
                contentArea.setWrapStyleWord(true);
                contentArea.setFont(new Font("Arial", Font.PLAIN, 16));

                JScrollPane scrollPane = new JScrollPane(contentArea);
                panel.add(scrollPane, unitName);
            }

            // Add components to subject panel
            JPanel subjectPanel = new JPanel(new BorderLayout());
            subjectPanel.add(new JLabel(name + " Study Guide", JLabel.CENTER), BorderLayout.NORTH);
            subjectPanel.add(unitNavPanel, BorderLayout.CENTER);

            panel.add(subjectPanel, "SubjectHome");
            subjectCardLayout.show(panel, "SubjectHome");
        }

        public void addUnit(String unitName, String content) {
            units.put(unitName, content);
        }

        public JPanel getPanel() {
            return panel;
        }

        public String getName() {
            return name;
        }

        private class UnitButtonListener implements ActionListener {
            private String unitName;

            public UnitButtonListener(String unitName) {
                this.unitName = unitName;
            }

            @Override
            public void actionPerformed(ActionEvent e) {
                subjectCardLayout.show(panel, unitName);
            }
        }
    }
}