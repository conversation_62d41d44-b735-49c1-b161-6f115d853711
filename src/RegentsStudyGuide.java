import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;

public class RegentsUhUhUhTheTheUhhhhhh extends J<PERSON>rame {
    private JTable table;
    private JPanel mainPanel;
    private String currentSubject = "Living Environment";

    public  RegentsUhUhUhTheTheUhhhhhh() {
        setTitle("Simple Study Guide");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1000, 800);
        setLocationRelativeTo(null);

        mainPanel = new JPanel(new BorderLayout());

        // Create subject selection at the top
        JPanel topPanel = new JPanel();
        JComboBox<String> subjectSelector = new JComboBox<>(new String[]{
                "Living Environment",
                "Global History",
                "Geometry",
                "Java Programming",
                "How to be a decent human being",
                "how to overcome your fear of saying the word Job"
        });
        subjectSelector.addActionListener(e -> updateContent((String)subjectSelector.getSelectedItem()));
        topPanel.add(new JLabel("Gurt: Yo "));
        topPanel.add(subjectSelector);

        // Create table
        String[] columnNames = {"Unit", "Key Topics"};
        table = new JTable();
        table.setRowHeight(100);  // Make rows taller for content
        table.getTableHeader().setReorderingAllowed(false);
        table.setDefaultEditor(Object.class, null);  // Make table non-editable

        // Style the table
        table.setFont(new Font("Arial", Font.PLAIN, 14));
        table.getTableHeader().setFont(new Font("Arial", Font.BOLD, 14));

        JScrollPane scrollPane = new JScrollPane(table);

        mainPanel.add(topPanel, BorderLayout.NORTH);
        mainPanel.add(scrollPane, BorderLayout.CENTER);

        add(mainPanel);
        updateContent("Living Environment");  // Load initial content
    }

    private void updateContent(String subject) {
        this.currentSubject = subject;  // Add this line if you plan to use it
        DefaultTableModel model = new DefaultTableModel(new Object[]{"Units? more like stupits", "Lock the freak in"}, 0);

        switch(subject) {
            case "Living Environment":
                model.addRow(new Object[]{"Scientific Inquiry",
                        "• Scientific Method\n• Experimental Design\n• Data Analysis\n• Lab Safety"});
                model.addRow(new Object[]{"Biochemistry",
                        "• Organic Compounds\n• Enzymes\n• Homeostasis\n• pH Scale"});
                model.addRow(new Object[]{"Cells",
                        "• Cell Theory\n• Organelles\n• Cell Membrane\n• Transport"});
                model.addRow(new Object[]{"Human Body Systems",
                        "• Nervous System\n• Circulatory System\n• Respiratory System\n• Digestive System"});
                break;

            case "Global History":
                model.addRow(new Object[]{"Ancient World",
                        "• River Valley Civilizations\n• Classical Empires\n• Major Religions\n• Trade Routes"});
                model.addRow(new Object[]{"Middle Ages",
                        "• Feudal Systems\n• Byzantine Empire\n• Islamic Golden Age\n• Crusades"});
                model.addRow(new Object[]{"The American Civil War",
                        "• Causes:\n" +
                                "  - Slavery and states' rights\n" +
                                "  - Economic differences between North and South\n" +
                                "  - Missouri Compromise of 1820\n" +
                                "  - Kansas-Nebraska Act of 1854\n" +
                                "  - Election of Abraham Lincoln (1860)\n\n" +
                                "• Reconstruction Period:\n" +
                                "  - Presidential vs. Radical Reconstruction\n" +
                                "  - 13th, 14th, and 15th Amendments\n" +
                                "  - Freedmen's Bureau\n" +
                                "  - Black Codes and Jim Crow Laws\n" +
                                "  - End of Reconstruction (1877)"});
                model.addRow(new Object[]{"Modern Era",
                        "• Industrial Revolution\n• Nationalism\n• Imperialism\n• World Wars\n• arms races"});
                break;

            case "Geometry":
                model.addRow(new Object[]{"Basics",
                        "• Points and Lines\n• Angles\n• Coordinate Geometry\n• Transformations"});
                model.addRow(new Object[]{"Triangles",
                        "• Congruence\n• Similarity\n• Special Right Triangles\n• Pythagorean Theorem"});
                model.addRow(new Object[]{"Circles",
                        "• Circle Equations\n• Arc Measures\n• Inscribed Angles\n• Tangent Lines"});
                break;

            case "Java Programming":
                model.addRow(new Object[]{"Basics",
                        "• Data Types\n• Variables\n• Operators\n• Arrays"});
                model.addRow(new Object[]{"Control Structures",
                        "• Conditionals\n• Loops\n• Switch Statements\n• Exception Handling"});
                model.addRow(new Object[]{"OOP",
                        "• Classes and Objects\n• Inheritance\n• Polymorphism\n• Encapsulation"});
                break;

            case "How to be a decent human being":
                model.addRow(new Object[]{"Basics",
                        "•Getting a job\n• Personal hygiene\n• personal awareness"});
                model.addRow(new Object[]{"How to get a job",
                        "• Apply for a job\n• job Resume\n• employment application"});
                model.addRow(new Object[]{"how to take care of your personal hygiene",
                        "• Showering\n• Shampoo\n• Brushing and Flossing teeth\n• Deoderant"});
                break;

            case "how to overcome your fear of saying the word Job":
                model.addRow(new Object[]{"Emotional preperation",
                        "•Look at the word\n• Come to terms\n• accept"});
                model.addRow(new Object[]{"how to pronounce the letter J",
                        "•understand what a voiced palatal approximan is\n• pronounce the Y sound\n• Have your tongue approach the roof of the mouth while pronouncing the letter y, but not touching it. this is similar to a platal glide"});
                break;
            table.setModel(model);

            // Set column widths
            table.getColumnModel().getColumn(0).setPreferredWidth(150);
            table.getColumnModel().getColumn(1).setPreferredWidth(450);
        }

        public static void main(String[] args) {
            SwingUtilities.invokeLater(() -> {
                new RegentsUhUhUhTheTheUhhhhhh().setVisible(true);
            });


        }
    }
