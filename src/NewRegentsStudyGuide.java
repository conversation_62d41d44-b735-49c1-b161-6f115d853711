import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;

public class NewRegentsStudyGuide extends JFrame {
    private JTable table;
    private JPanel mainPanel;
    private String currentSubject = "Living Environment";  // Default subject

    public NewRegentsStudyGuide() {
        setTitle("Simple Study Guide");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(800, 600);
        setLocationRelativeTo(null);

        mainPanel = new JPanel(new BorderLayout());

        // Create subject selection at the top
        JPanel topPanel = new JPanel();
        JComboBox<String> subjectSelector = new JComboBox<>(new String[]{
                "Living Environment",
                "Global History",
                "Geometry",
                "Java Programming"
        });
        subjectSelector.addActionListener(e -> updateContent((String)subjectSelector.getSelectedItem()));
        topPanel.add(new JLabel("Select Subject: "));
        topPanel.add(subjectSelector);

        // Create table
        String[] columnNames = {"Unit", "Key Topics"};
        table = new JTable();
        table.setRowHeight(100);  // Make rows taller for content
        table.getTableHeader().setReorderingAllowed(false);
        table.setDefaultEditor(Object.class, null);  // Make table non-editable

        // Style the table
        table.setFont(new Font("Arial", Font.PLAIN, 14));
        table.getTableHeader().setFont(new Font("Arial", Font.BOLD, 14));

        JScrollPane scrollPane = new JScrollPane(table);

        mainPanel.add(topPanel, BorderLayout.NORTH);
        mainPanel.add(scrollPane, BorderLayout.CENTER);

        add(mainPanel);
        updateContent("Living Environment");  // Load initial content
    }

    private void updateContent(String subject) {
        DefaultTableModel model = new DefaultTableModel(new Object[]{"Unit", "Key Topics"}, 0);

        switch(subject) {
            case "Living Environment":
                model.addRow(new Object[]{"Scientific Inquiry",
                        "• Scientific Method\n• Experimental Design\n• Data Analysis\n• Lab Safety"});
                model.addRow(new Object[]{"Biochemistry",
                        "• Organic Compounds\n• Enzymes\n• Homeostasis\n• pH Scale"});
                model.addRow(new Object[]{"Cells",
                        "• Cell Theory\n• Organelles\n• Cell Membrane\n• Transport"});
                model.addRow(new Object[]{"Human Body Systems",
                        "• Nervous System\n• Circulatory System\n• Respiratory System\n• Digestive System"});
                break;

            case "Global History":
                model.addRow(new Object[]{"Ancient World",
                        "• River Valley Civilizations\n• Classical Empires\n• Major Religions\n• Trade Routes"});
                model.addRow(new Object[]{"Middle Ages",
                        "• Feudal Systems\n• Byzantine Empire\n• Islamic Golden Age\n• Crusades"});
                model.addRow(new Object[]{"Modern Era",
                        "• Industrial Revolution\n• Nationalism\n• Imperialism\n• World Wars"});
                break;

            case "Geometry":
                model.addRow(new Object[]{"Basics",
                        "• Points and Lines\n• Angles\n• Coordinate Geometry\n• Transformations"});
                model.addRow(new Object[]{"Triangles",
                        "• Congruence\n• Similarity\n• Special Right Triangles\n• Pythagorean Theorem"});
                model.addRow(new Object[]{"Circles",
                        "• Circle Equations\n• Arc Measures\n• Inscribed Angles\n• Tangent Lines"});
                break;

            case "Java Programming":
                model.addRow(new Object[]{"Basics",
                        "• Data Types\n• Variables\n• Operators\n• Arrays"});
                model.addRow(new Object[]{"Control Structures",
                        "• Conditionals\n• Loops\n• Switch Statements\n• Exception Handling"});
                model.addRow(new Object[]{"OOP",
                        "• Classes and Objects\n• Inheritance\n• Polymorphism\n• Encapsulation"});
                break;
        }

        table.setModel(model);

        // Set column widths
        table.getColumnModel().getColumn(0).setPreferredWidth(150);
        table.getColumnModel().getColumn(1).setPreferredWidth(450);
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            new NewRegentsStudyGuide().setVisible(true);
        });
    }
}